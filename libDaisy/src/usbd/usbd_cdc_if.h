/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : usbd_cdc_if.h
  * @version        : v1.0_Cube
  * @brief          : Header for usbd_cdc_if.c file.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2019 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/**< Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USBD_CDC_IF_H__
#define __USBD_CDC_IF_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "usbd_cdc.h"

    /* USER CODE BEGIN INCLUDE */

    /* USER CODE END INCLUDE */

    /** @addtogroup STM32_USB_OTG_DEVICE_LIBRARY
  * @brief For Usb device.
  * @{
  */

    /** @defgroup USBD_CDC_IF USBD_CDC_IF
  * @brief Usb VCP device module
  * @{
  */

    /** @defgroup USBD_CDC_IF_Exported_Defines USBD_CDC_IF_Exported_Defines
  * @brief Defines.
  * @{
  */
    /* USER CODE BEGIN EXPORTED_DEFINES */

    /* USER CODE END EXPORTED_DEFINES */

    /**
  * @}
  */

    /** @defgroup USBD_CDC_IF_Exported_Types USBD_CDC_IF_Exported_Types
  * @brief Types.
  * @{
  */

    /* USER CODE BEGIN EXPORTED_TYPES */
    /**
       \param buf buffer
       \param size buffer size
    */
    typedef void (*CDC_ReceiveCallback)(uint8_t* buf, uint32_t* size);

    /* USER CODE END EXPORTED_TYPES */

    /**
  * @}
  */

    /** @defgroup USBD_CDC_IF_Exported_Macros USBD_CDC_IF_Exported_Macros
  * @brief Aliases.
  * @{
  */

    /* USER CODE BEGIN EXPORTED_MACRO */

    /* USER CODE END EXPORTED_MACRO */

    /**
  * @}
  */

    /** @defgroup USBD_CDC_IF_Exported_Variables USBD_CDC_IF_Exported_Variables
  * @brief Public variables.
  * @{
  */

    /** CDC Interface callback. */
    extern USBD_CDC_ItfTypeDef USBD_Interface_fops_FS;
    /** CDC Interface callback. */
    extern USBD_CDC_ItfTypeDef USBD_Interface_fops_HS;

    /* USER CODE BEGIN EXPORTED_VARIABLES */

    /* USER CODE END EXPORTED_VARIABLES */

    /**
  * @}
  */

    /** @defgroup USBD_CDC_IF_Exported_FunctionsPrototype USBD_CDC_IF_Exported_FunctionsPrototype
  * @brief Public functions declaration.
  * @{
  */
    void    CDC_Set_Rx_Callback_FS(CDC_ReceiveCallback cb); /**< & */
    void    CDC_Set_Rx_Callback_HS(CDC_ReceiveCallback cb); /**< & */
    uint8_t CDC_Transmit_FS(uint8_t* Buf, uint16_t Len);    /**< & */
    uint8_t CDC_Transmit_HS(uint8_t* Buf, uint16_t Len);    /**< & */

    /* USER CODE BEGIN EXPORTED_FUNCTIONS */


    /* USER CODE END EXPORTED_FUNCTIONS */

    /**
  * @}
  */

    /**
  * @}
  */

    /**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __USBD_CDC_IF_H__ */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
