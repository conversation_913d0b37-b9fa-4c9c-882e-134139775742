# libDaisy

libDaisy is a C++ hardware support library for the Electrosmith Daisy platform.

## Setting Up Your Development Environment

* [Getting Started](https://github.com/electro-smith/DaisyWiki/wiki)
* [Setting Up the Development Environment](https://github.com/electro-smith/DaisyWiki/wiki/1.-Setting-Up-Your-Development-Environment)
* [Creating a New Project](https://github.com/electro-smith/DaisyWiki/wiki/How-To:-Create-a-New-Project)

## Using libDaisy

* [Getting Started - GPIO](_a1_Getting-Started-GPIO.md)
* [Getting Started - Serial Printing](_a2_Getting-Started-Serial-Printing.md)
* [Getting Started - Audio](_a3_Getting-Started-Audio.md)
* [Getting Started - the ADC Inputs](_a4_Getting-Started-ADCs.md)
* [Getting Started - External SDRAM](_a6_Getting-Started-External-SDRAM.md)
* [Getting Started - Daisy Bootloader](_a7_Getting-Started-Daisy-Bootloader.md)
* [Getting Started - SPI](_a8_Getting-Started-SPI.md)

## Development

* [Running and Writing Unit Tests](_b1_Development-Unit-Testing.md)

## Troubleshooting

Report bugs, typos, errors, etc. [here on GitHub](https://github.com/electro-smith/libDaisy/issues)
