{
    "version": "2.0.0",
    "type": "shell",
    "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "dedicated"
    },
    "windows": {
        "options": {}
    },
    "tasks": [
        {
            "label": "build-libDaisy-debug",
            "osx": {
                "command": "make",
                "args": [
                    "all",
                    "DEBUG=1"
                ]
            },
            "linux": {
                "command": "make",
                "args": [
                    "all",
                    "DEBUG=1"
                ]
            },
            "windows": {
                "command": "make.exe",
                "args": [
                    "all",
                    "DEBUG=1"
                ]
            },
            "options": {
                "cwd": "${workspaceRoot}"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "build-libDaisy-release",
            "osx": {
                "command": "make",
                "args": [
                    "all"
                ]
            },
            "linux": {
                "command": "make",
                "args": [
                    "all"
                ]
            },
            "windows": {
                "command": "make.exe",
                "args": [
                    "all"
                ]
            },
            "options": {
                "cwd": "${workspaceRoot}"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "clean-libDaisy",
            "osx": {
                "command": "make",
                "args": [
                    "clean"
                ]
            },
            "linux": {
                "command": "make",
                "args": [
                    "clean"
                ]
            },
            "windows": {
                "command": "make.exe",
                "args": [
                    "clean"
                ]
            },
            "options": {
                "cwd": "${workspaceRoot}"
            },
            "problemMatcher": []
        },
        {
            "label": "clean-build-debug",
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence",
            "dependsOn": [
                "clean-libDaisy",
                "build-libDaisy-debug",
            ],
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "clean-build-release",
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence",
            "dependsOn": [
                "clean-libDaisy",
                "build-libDaisy-release",
            ],
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "build-libDaisy-tests",
            "command": "make",
            "args": [
                "DEBUG=1"
            ],
            "options": {
                "cwd": "${workspaceFolder}/tests"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}/tests"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "clean-libDaisy-tests",
            "command": "make",
            "args": [
                "clean"
            ],
            "options": {
                "cwd": "${workspaceFolder}/tests"
            },
            "problemMatcher": []
        },
        {
            "label": "clean-build-libDaisy-tests",
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence",
            "dependsOn": [
                "clean-libDaisy-tests",
                "build-libDaisy-tests"
            ],
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}/tests"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
    ]
}