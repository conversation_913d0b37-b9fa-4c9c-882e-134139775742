<?xml version="1.0"?>
<VisualGDBProjectSettings2 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <ConfigurationName>Release</ConfigurationName>
  <Project xsi:type="com.visualgdb.project.embedded">
    <CustomSourceDirectories>
      <Directories />
      <PathStyle>MinGWUnixSlash</PathStyle>
      <LocalDirForAbsolutePaths>$(ToolchainDir)</LocalDirForAbsolutePaths>
    </CustomSourceDirectories>
    <MainSourceDirectory>$(ProjectDir)</MainSourceDirectory>
    <ExportAdvancedBuildVariables>false</ExportAdvancedBuildVariables>
    <EmbeddedProfileFile>stm32.xml</EmbeddedProfileFile>
  </Project>
  <Build xsi:type="com.visualgdb.build.msbuild">
    <ToolchainID>
      <ID>com.sysprogs.gnuarm.arm-eabi</ID>
      <Version>
        <GCC>8.3.1</GCC>
        <GDB>8.0</GDB>
        <Revision>1</Revision>
      </Version>
    </ToolchainID>
    <ProjectFile>libdaisy.vcxproj</ProjectFile>
    <RemoteBuildEnvironment>
      <Records />
    </RemoteBuildEnvironment>
    <ParallelJobCount>1</ParallelJobCount>
    <SuppressDirectoryChangeMessages>true</SuppressDirectoryChangeMessages>
  </Build>
  <CustomBuild>
    <PreSyncActions />
    <PreBuildActions />
    <PostBuildActions />
    <PreCleanActions />
    <PostCleanActions />
  </CustomBuild>
  <CustomDebug>
    <PreDebugActions />
    <PostDebugActions />
    <DebugStopActions />
    <BreakMode>Default</BreakMode>
  </CustomDebug>
  <CustomShortcuts>
    <Shortcuts />
    <ShowMessageAfterExecuting>true</ShowMessageAfterExecuting>
  </CustomShortcuts>
  <UserDefinedVariables />
  <ImportedPropertySheets />
  <CodeSense>
    <Enabled>Unknown</Enabled>
    <ExtraSettings>
      <HideErrorsInSystemHeaders>true</HideErrorsInSystemHeaders>
      <SupportLightweightReferenceAnalysis>true</SupportLightweightReferenceAnalysis>
      <CheckForClangFormatFiles xsi:nil="true" />
      <FormattingEngine xsi:nil="true" />
    </ExtraSettings>
    <CodeAnalyzerSettings>
      <Enabled>false</Enabled>
    </CodeAnalyzerSettings>
  </CodeSense>
  <BuildContextDirectory>VisualGDB\VisualGDBCache</BuildContextDirectory>
  <Configurations />
  <ProgramArgumentsSuggestions />
  <Debug xsi:type="com.visualgdb.debug.embedded">
    <AdditionalStartupCommands />
    <AdditionalGDBSettings>
      <Features>
        <DisableAutoDetection>false</DisableAutoDetection>
        <UseFrameParameter>false</UseFrameParameter>
        <SimpleValuesFlagSupported>false</SimpleValuesFlagSupported>
        <ListLocalsSupported>false</ListLocalsSupported>
        <ByteLevelMemoryCommandsAvailable>false</ByteLevelMemoryCommandsAvailable>
        <ThreadInfoSupported>false</ThreadInfoSupported>
        <PendingBreakpointsSupported>false</PendingBreakpointsSupported>
        <SupportTargetCommand>false</SupportTargetCommand>
        <ReliableBreakpointNotifications>false</ReliableBreakpointNotifications>
      </Features>
      <EnableSmartStepping>false</EnableSmartStepping>
      <FilterSpuriousStoppedNotifications>false</FilterSpuriousStoppedNotifications>
      <ForceSingleThreadedMode>false</ForceSingleThreadedMode>
      <UseAppleExtensions>false</UseAppleExtensions>
      <CanAcceptCommandsWhileRunning>false</CanAcceptCommandsWhileRunning>
      <MakeLogFile>false</MakeLogFile>
      <IgnoreModuleEventsWhileStepping>true</IgnoreModuleEventsWhileStepping>
      <UseRelativePathsOnly>false</UseRelativePathsOnly>
      <ExitAction>None</ExitAction>
      <DisableDisassembly>false</DisableDisassembly>
      <ExamineMemoryWithXCommand>false</ExamineMemoryWithXCommand>
      <StepIntoNewInstanceEntry>main</StepIntoNewInstanceEntry>
      <ExamineRegistersInRawFormat>true</ExamineRegistersInRawFormat>
      <DisableSignals>false</DisableSignals>
      <EnableAsyncExecutionMode>false</EnableAsyncExecutionMode>
      <EnableNonStopMode>false</EnableNonStopMode>
      <MaxBreakpointLimit>0</MaxBreakpointLimit>
    </AdditionalGDBSettings>
    <DebugMethod>
      <ID>com.sysprogs.arm.openocd</ID>
      <Configuration xsi:type="com.visualgdb.edp.openocd.settings">
        <CommandLine>-f interface/stlink.cfg -f target/stm32h7x.cfg -c init -c "reset init"</CommandLine>
        <ExtraParameters>
          <Frequency xsi:nil="true" />
          <BoostedFrequency xsi:nil="true" />
          <ConnectUnderReset>false</ConnectUnderReset>
        </ExtraParameters>
        <LoadProgressGUIThreshold>131072</LoadProgressGUIThreshold>
        <ProgramMode>Enabled</ProgramMode>
        <StartupCommands>
          <string>set remotetimeout 60</string>
          <string>target remote :$$SYS:GDB_PORT$$</string>
          <string>mon halt</string>
          <string>mon reset init</string>
          <string>load</string>
        </StartupCommands>
        <ProgramFLASHUsingExternalTool>false</ProgramFLASHUsingExternalTool>
        <PreferredGDBPort>0</PreferredGDBPort>
        <PreferredTelnetPort>0</PreferredTelnetPort>
      </Configuration>
    </DebugMethod>
    <AutoDetectRTOS>true</AutoDetectRTOS>
    <SemihostingSupport>Auto</SemihostingSupport>
    <SemihostingPollingDelay>0</SemihostingPollingDelay>
    <StepIntoEntryPoint>false</StepIntoEntryPoint>
    <ReloadFirmwareOnReset>false</ReloadFirmwareOnReset>
    <ValidateEndOfStackAddress>true</ValidateEndOfStackAddress>
    <StopAtEntryPoint>false</StopAtEntryPoint>
    <EnableVirtualHalts>false</EnableVirtualHalts>
    <DynamicAnalysisSettings />
    <EndOfStackSymbol>_estack</EndOfStackSymbol>
    <TimestampProviderTicksPerSecond>0</TimestampProviderTicksPerSecond>
    <KeepConsoleAfterExit>false</KeepConsoleAfterExit>
    <CheckInterfaceDrivers>true</CheckInterfaceDrivers>
  </Debug>
</VisualGDBProjectSettings2>