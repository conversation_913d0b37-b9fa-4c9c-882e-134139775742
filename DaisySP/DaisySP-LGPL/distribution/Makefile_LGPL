# Special Makefile for re-linking LGPL libraries

TARGET_BIN=$(TARGET).bin
TARGET_ELF=$(TARGET).elf

# If you have the arm-none-eabi- toolchain located in a particular place, but not installed for the entire system, add the path here:
# GCC_PATH=

# Build path
BUILD_DIR = build
RESOURCE_DIR = resource

#######################################
# binaries
#######################################
PREFIX = arm-none-eabi-
# The gcc compiler bin path can be either defined in make command via GCC_PATH variable (> make GCC_PATH=xxx)
# either it can be added to the PATH environment variable.
ifdef GCC_PATH
AS = $(GCC_PATH)/$(PREFIX)gcc -x assembler-with-cpp
CP = $(GCC_PATH)/$(PREFIX)objcopy
else
CXX = $(PREFIX)g++ 
CP = $(PREFIX)objcopy
endif
HEX = $(CP) -O ihex
BIN = $(CP) -O binary -S

#######################################
# CFLAGS
#######################################
# cpu
CPU = -mcpu=cortex-m7

# fpu
FPU = -mfpu=fpv5-d16

# float-abi
FLOAT-ABI = -mfloat-abi=hard

# mcu
MCU = $(CPU) -mthumb $(FPU) $(FLOAT-ABI)

#######################################
# Boot Management
#######################################

INTERNAL_ADDRESS = 0x08000000
QSPI_ADDRESS ?= 0x90040000

# For the time being, we'll keep the Daisy Bootloader's PID as df11
# DAISY_PID = a360
DAISY_PID = df11
STM_PID = df11

BOOT_BIN ?= $(wildcard $(RESOURCE_DIR)/dsy_bootloader*)
APP_TYPE ?= BOOT_NONE

ifeq ($(APP_TYPE), BOOT_NONE)

LDSCRIPT ?= $(RESOURCE_DIR)/STM32H750IB_flash.lds
USBPID = $(STM_PID)
FLASH_ADDRESS ?= $(INTERNAL_ADDRESS)

else ifeq ($(APP_TYPE), BOOT_SRAM)

LDSCRIPT ?= $(RESOURCE_DIR)/STM32H750IB_sram.lds
USBPID = $(DAISY_PID)
FLASH_ADDRESS ?= $(QSPI_ADDRESS)
C_DEFS += -DBOOT_APP

else ifeq ($(APP_TYPE), BOOT_QSPI)

LDSCRIPT ?= $(RESOURCE_DIR)/STM32H750IB_qspi.lds
USBPID = $(DAISY_PID)
FLASH_ADDRESS ?= $(QSPI_ADDRESS)
C_DEFS += -DBOOT_APP

else

$(error Unkown app type "$(APP_TYPE)")

endif

#######################################
# LDFLAGS
#######################################
# libraries
LIBS += -ldaisy -lc -lm -lnosys
LIBDIR += -L $(RESOURCE_DIR)
#LIBDIR = -L./VisualGDB/Release

LIBS += -ldaisysp
LIBDIR += -L $(RESOURCE_DIR)

LIBS += -ldaisysp-lgpl
LIBDIR += -L $(RESOURCE_DIR)

LDFLAGS ?=
LDFLAGS += $(MCU) --specs=nano.specs --specs=nosys.specs -T$(LDSCRIPT) $(LIBDIR) $(LIBS) -Wl,-Map=$(RESOURCE_DIR)/$(TARGET).map,--cref -Wl,--gc-sections -Wl,--print-memory-usage

# default action: build all
all: $(BUILD_DIR) $(BUILD_DIR)/$(TARGET).elf $(BUILD_DIR)/$(TARGET).hex $(BUILD_DIR)/$(TARGET).bin

#######################################
# build the application
#######################################

OBJECTS = $(wildcard $(RESOURCE_DIR)/*.o)

$(BUILD_DIR)/$(TARGET).elf: Makefile
	$(CXX) $(OBJECTS) $(LDFLAGS) -o $@

$(BUILD_DIR)/%.hex: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	$(HEX) $< $@

$(BUILD_DIR)/%.bin: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	$(BIN) $< $@

$(BUILD_DIR):
	mkdir $@

#######################################
# clean up
#######################################
clean:
	-rm -fR $(BUILD_DIR)


#######################################
# dfu-util
#######################################

program-dfu:
	dfu-util -a 0 -s $(FLASH_ADDRESS):leave -D $(BUILD_DIR)/$(TARGET_BIN) -d ,0483:$(USBPID)

program-boot:
	dfu-util -a 0 -s $(INTERNAL_ADDRESS):leave -D $(BOOT_BIN) -d ,0483:$(STM_PID)

#######################################
# dependencies
#######################################
-include $(wildcard $(BUILD_DIR)/*.d)

# *** EOF ***
