*.obj
*.exe
*.dll
*.db
*.ilk
*.pdb
*.tlog
*.cache
*.idb
*.ipch
Log/
Debug/
Release/
x64/
.static/
*.prefs
__MACOSX/
_MACOSX/
.build/
.metadata/
.build_Debug/
*.d
.build_Release/
Intermediate/
*.plist
*.o
*.sdf
*.opendb
*.l##
*.l#9
*.l#8
*.l#7
*.l#6
*.l#5
*.l#4
*.l#3
*.l#2
*.l#1
*.l#0
*.s##
*.s#9
*.s#8
*.s#7
*.s#6
*.s#5
*.s#4
*.s#3
*.s#2
*.s#1
*.s#0
*.b##
*.b#9
*.b#8
*.b#7
*.b#6
*.b#5
*.b#4
*.b#3
*.b#2
*.b#1
*.b#0
*.l$$
*.l$9
*.l$8
*.l$7
*.l$6
*.l$5
*.l$4
*.l$3
*.l$2
*.l$1
*.l$0
*.s$$
*.s$9
*.s$8
*.s$7
*.s$6
*.s$5
*.s$4
*.s$3
*.s$2
*.s$1
*.s$0
*.b$$
*.b$9
*.b$8
*.b$7
*.b$6
*.b$5
*.b$4
*.b$3
*.b$2
*.b$1
*.b$0
Backup_of_*
*.gpk
*~
Intermediate-llvm/
*.exp
NONE
temp.txt
.vs/
*.pyc
windows_amd64/
*.mov
*.mp4
sw/tables/
Universal45/
LLVM/
._.DS_Store
.DS_Store
*.lib
*.bak
*.suo
*.user
.ipynb_checkpoints/
transcode report.txt
/build
/examples/*/build
/doc/*.md
/doc/html/
/doc/latex/
/**/VisualGDB
/**/build
.visualgdb
VisualGDBCache
vs/*.log

