<?xml version="1.0"?>
<EmbeddedProfile xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <ToolchainID>com.visualgdb.arm-eabi</ToolchainID>
  <ToolchainVersion>
    <GCC>9.3.1</GCC>
    <GDB>9.2.0</GDB>
    <Revision>2</Revision>
  </ToolchainVersion>
  <BspID>com.sysprogs.arm.stm32</BspID>
  <BspVersion>2020.10</BspVersion>
  <McuID>STM32H750IB</McuID>
  <MCUDefinitionFile>STM32H7xxxx/DeviceDefinitions/stm32h750xx.xml</MCUDefinitionFile>
  <MCUProperties>
    <Entries>
      <KeyValue>
        <Key>com.sysprogs.bspoptions.primary_memory</Key>
        <Value>flash</Value>
      </KeyValue>
      <KeyValue>
        <Key>com.sysprogs.bspoptions.arm.floatmode</Key>
        <Value>-mfloat-abi=hard</Value>
      </KeyValue>
      <KeyValue>
        <Key>com.sysprogs.mcuoptions.ignore_startup_file</Key>
      </KeyValue>
      <KeyValue>
        <Key>com.sysprogs.toolchainoptions.arm.libctype</Key>
        <Value>--specs=nano.specs</Value>
      </KeyValue>
      <KeyValue>
        <Key>com.sysprogs.toolchainoptions.arm.syscallspecs</Key>
        <Value>--specs=nosys.specs</Value>
      </KeyValue>
    </Entries>
  </MCUProperties>
  <BSPSourceFolderName>Device-specific files</BSPSourceFolderName>
  <MCUMakFile>stm32.mak</MCUMakFile>
  <ReferencedFrameworks />
  <FrameworkProperties>
    <Entries>
      <KeyValue>
        <Key>com.sysprogs.bspoptions.stm32.ll_driver</Key>
        <Value />
      </KeyValue>
    </Entries>
  </FrameworkProperties>
  <TestFrameworkProperties>
    <Entries />
  </TestFrameworkProperties>
</EmbeddedProfile>