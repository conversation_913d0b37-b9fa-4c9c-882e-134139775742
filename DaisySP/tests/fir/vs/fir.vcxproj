﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|VisualGDB">
      <Configuration>Debug</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|VisualGDB">
      <Configuration>Release</Configuration>
      <Platform>VisualGDB</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{2CDA3075-61C7-433F-A065-D5182FB60151}</ProjectGuid>
    <BSP_ID>com.sysprogs.arm.stm32</BSP_ID>
    <BSP_VERSION>2020.10</BSP_VERSION>
    <InPlaceBSPSubdir />
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <MCUPropertyListFile>$(ProjectDir)stm32.props</MCUPropertyListFile>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">
    <MCUPropertyListFile>$(ProjectDir)stm32.props</MCUPropertyListFile>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <PropertyGroup Label="UserMacros">
    <LibDaisyDir>../../../../libdaisy</LibDaisyDir>
    <DaisySpDir>../../../../daisysp</DaisySpDir>
    <CmsisDir>$(LibDaisyDir)/Drivers/CMSIS</CmsisDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <GNUConfigurationType>Debug</GNUConfigurationType>
    <ToolchainID>4f37eb33-03c0-4029-8c13-2c908cdc2270</ToolchainID>
    <ToolchainVersion>9.3.1/(GNU/r0</ToolchainVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">
    <ToolchainID>4f37eb33-03c0-4029-8c13-2c908cdc2270</ToolchainID>
    <ToolchainVersion>9.3.1/(GNU/r0</ToolchainVersion>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>$(DaisySpDir)/Source;$(LibDaisyDir)/src;$(LibDaisyDir)/src/sys;$(LibDaisyDir)/src/usbd;$(CmsisDir)/Include;$(CmsisDir)/DSP/Include;$(CmsisDir)/Device/ST/STM32H7xx/Include;../../util;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_ARM_DSP;DEBUG=1;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <ForcedIncludeFiles>$(CmsisDir)/Device/ST/STM32H7xx/Include/stm32h7xx.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>;%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>;%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>;%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>$(LibDaisyDir)/core/STM32H750IB_flash.lds</LinkerScript>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">
    <ClCompile>
      <AdditionalIncludeDirectories>$(DaisySpDir)/Source;$(LibDaisyDir)/src;$(LibDaisyDir)/src/sys;$(LibDaisyDir)/src/usbd;$(CmsisDir)/Include;$(CmsisDir)/DSP/Include;$(CmsisDir)/Device/ST/STM32H7xx/Include;../../util;%(ClCompile.AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USE_ARM_DSP;NDEBUG=1;RELEASE=1;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions />
      <CLanguageStandard />
      <CPPLanguageStandard />
      <ForcedIncludeFiles>$(CmsisDir)/Device/ST/STM32H7xx/Include/stm32h7xx.h</ForcedIncludeFiles>
    </ClCompile>
    <Link>
      <AdditionalLinkerInputs>%(Link.AdditionalLinkerInputs)</AdditionalLinkerInputs>
      <LibrarySearchDirectories>%(Link.LibrarySearchDirectories)</LibrarySearchDirectories>
      <AdditionalLibraryNames>%(Link.AdditionalLibraryNames)</AdditionalLibraryNames>
      <LinkerScript>$(LibDaisyDir)/core/STM32H750IB_flash.lds</LinkerScript>
      <AdditionalOptions />
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;_CRT_NONSTDC_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(DaisySpDir)/Source;$(ProjectDir)../../util;$(ProjectDir)../../util\daisy_pc;$(LibDaisyDir)/src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <AdditionalOptions>/Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <Profile>true</Profile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;_CRT_NONSTDC_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(DaisySpDir)/Source;$(ProjectDir)../../util;$(ProjectDir)../../util\daisy_pc;$(LibDaisyDir)/src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp14</LanguageStandard>
      <AdditionalOptions>/Zc:__cplusplus %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <Profile>true</Profile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="$(DaisySpDir)\vs\daisysp.vcxproj">
      <Project>{44f4aea3-a6b2-4f03-9c28-34b2336bdf57}</Project>
    </ProjectReference>
    <ProjectReference Include="$(LibDaisyDir)\libdaisy.vcxproj">
      <Project>{1b07a9d3-1e1b-488a-9817-4b13eeca191c}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ItemGroup>
    <ClCompile Include="$(LibDaisyDir)\core\startup_stm32h750xx.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="$(LibDaisyDir)\src\hid\logger.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|VisualGDB'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|VisualGDB'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="$(CmsisDir)\DSP\Source\FilteringFunctions\arm_fir_f32.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="$(CmsisDir)\DSP\Source\FilteringFunctions\arm_fir_init_f32.c">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="..\tst_fir.cpp" />
    <None Include="stm32.props" />
    <None Include="fir-Debug.vgdbsettings" />
    <None Include="fir-Release.vgdbsettings" />
    <None Include="stm32.xml" />
  </ItemGroup>
</Project>