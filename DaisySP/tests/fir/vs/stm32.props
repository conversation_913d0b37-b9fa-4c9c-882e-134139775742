<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
<!-- 
	This file is generated by VisualGDB.
	It contains GCC settings automatically derived from the board support package (BSP).
	DO NOT EDIT MANUALLY. THE FILE WILL BE OVERWRITTEN. 
	Use VisualGDB Project Properties dialog or modify Makefile or per-configuration .mak files instead.
-->

<!-- In order to build this project manually (outside VisualGDB), please provide TOOLCHAIN_ROOT, BSP_ROOT, EFP_BASE and TESTFW_BASE variables via Environment or Make command line -->
	<ItemDefinitionGroup>
		<ClCompile>
			<PreprocessorDefinitions>ARM_MATH_CM7;flash_layout;CORE_CM7;STM32H750IB;%(ClCompile.PreprocessorDefinitions)</PreprocessorDefinitions>
			<AdditionalIncludeDirectories>%(ClCompile.AdditionalIncludeDirectories);$(ProjectDir)</AdditionalIncludeDirectories>
		</ClCompile>
	</ItemDefinitionGroup>
	<ItemDefinitionGroup>
		<Link>
			<LinkerScript Condition="'%(Link.LinkerScript)' == ''">$(BSP_ROOT)/STM32H7xxxx/LinkerScripts/STM32H750IB_flash.lds</LinkerScript>
			<AdditionalOptions>--specs=nano.specs --specs=nosys.specs %(Link.AdditionalOptions)</AdditionalOptions>
		</Link>
	</ItemDefinitionGroup>
	<PropertyGroup>
		<DefaultLinkerScript>$(BSP_ROOT)/STM32H7xxxx/LinkerScripts/STM32H750IB_flash.lds</DefaultLinkerScript>
	</PropertyGroup>

	<ItemDefinitionGroup>
		<ToolchainSettingsContainer>
			<ARMCPU  Condition="'%(ToolchainSettingsContainer.ARMCPU)' == ''">cortex-m7</ARMCPU>
			<InstructionSet  Condition="'%(ToolchainSettingsContainer.InstructionSet)' == ''">THUMB</InstructionSet>
			<ARMFPU  Condition="'%(ToolchainSettingsContainer.ARMFPU)' == ''">fpv5-d16</ARMFPU>
			<FloatABI  Condition="'%(ToolchainSettingsContainer.FloatABI)' == ''">hard</FloatABI>
		</ToolchainSettingsContainer>
	</ItemDefinitionGroup>
</Project>
